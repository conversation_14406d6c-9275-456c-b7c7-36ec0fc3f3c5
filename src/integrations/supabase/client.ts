// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/integrations/supabase/types';

const SUPABASE_URL = "https://iakmstxuamcnrsvjpuyn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imlha21zdHh1YW1jbnJzdmpwdXluIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyNDU2MjQsImV4cCI6MjA2NTgyMTYyNH0.rodDqc58YjmU-JgQq3BZuOGvIxzwmdT8T88tu9pw4uw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);